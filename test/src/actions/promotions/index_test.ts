import _ from 'lodash';
import assert from 'assert';
import sinon from 'sinon';
import uuid from 'uuid';
import moment from 'moment';
import { expect } from 'chai';
import { Op } from 'sequelize';

import * as check from '../../../../src/actions/promotions/check';
import cache from '../../../../src/config/cache';
import {
  Combinator,
  PromotionInstance,
  PromotionAttributes,
} from '../../../../src/models/promotion';
import { Promotion } from '../../../../src/models';
import * as PromotionsUsage from '../../../../src/actions/promotions_usage/create';

import {
  applyCombinator,
  checkCouponUsage,
} from '../../../../src/actions/promotions/check';

type Params = {
  hostId: string;
} & Partial<PromotionAttributes>;

const sortBy = arr => arr.sort((a, b) => (a.name < b.name ? -1 : 1));

export function promotionFactory(params: Params): PromotionInstance {
  return Promotion.build({
    id: uuid.v4(),
    name: Math.random().toString(),
    description: '',
    conditions: [],
    sponsored: false,
    effects: [],
    usage: 0,
    createdById: uuid.v4(),
    updatedById: uuid.v4(),
    startAt: new Date(Date.now() - 1000).toISOString(),
    finishAt: new Date(Date.now() + 1000).toISOString(),
    hostId: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...params,
  });
}

describe('all', () => {
  describe('xor', () => {
    it('truth table', () => {
      [
        [false, false, false],
        [false, true, true],
        [true, false, true],
        [true, true, false],
      ].forEach(([a, b, r]) => {
        expect(_.isEqual(check.xor(a, b), r)).to.be.true;
      });
    });
  });

  describe('findPromotions', () => {
    let supplierId: string;
    let retailerId: string;
    let promotions: Array<PromotionInstance>;
    let promotionsInclCoupons: Array<PromotionInstance>;
    let promotionsInclCouponsWithNoFilter: Array<PromotionInstance>;
    const couponName = 'TEST5';
    const couponWithNoFilter = 'TEST6';

    beforeEach(async () => {
      supplierId = uuid.v4();
      retailerId = uuid.v4();
      const conditions = {
        leftNode: 'supplierId',
        rightNode: supplierId,
        combinator: 'equal',
      };
      const p1 = await promotionFactory({
        name: 'p1',
        hostId: supplierId,
        conditions,
      }).save();
      const p2 = await promotionFactory({
        name: 'p2',
        hostId: supplierId,
        conditions,
      }).save();
      const couponPromotion = await promotionFactory({
        hostId: supplierId,
        name: couponName,
        coupon: couponName,
        conditions,
      }).save();

      const couponPromotionWithNoFilter = await promotionFactory({
        hostId: supplierId,
        name: couponWithNoFilter,
        coupon: couponWithNoFilter,
        conditions: [],
      }).save();
      promotions = await Promotion.findAll({
        where: { id: { [Op.in]: [p1.id, p2.id] } },
      });
      promotionsInclCoupons = await Promotion.findAll({
        where: { id: { [Op.in]: [p1.id, p2.id, couponPromotion.id] } },
      });
      promotionsInclCouponsWithNoFilter = await Promotion.findAll({
        where: {
          id: { [Op.in]: [p1.id, p2.id, couponPromotionWithNoFilter.id] },
        },
      });
      promotions = sortBy(promotions);
    });

    afterEach(async () => {
      await Promotion.destroy({
        where: {
          id: { [Op.in]: promotions.map(p => p.id) },
        },
      });
      supplierId = null;
      retailerId = null;
      promotions = null;
    });

    it('finds promotions matching the cart supplier OR ordermentum', async () => {
      const ps = await check.findPromotions({
        supplierId,
        retailerId,
        profile: {},
        userId: '',
      });
      expect(sortBy(ps)).to.deep.equal(promotions);
    });

    it('ignores other supplier promotions', async () => {
      const otherSupplierId = uuid.v4();
      await promotionFactory({ hostId: otherSupplierId }).save();

      const ps = await check.findPromotions({
        supplierId,
        profile: {},
        userId: '',
        retailerId,
      });
      expect(sortBy(ps)).to.deep.equal(promotions);
    });

    it('finds coupon code if coupon code sent', async () => {
      const otherSupplierId = uuid.v4();
      await promotionFactory({ hostId: otherSupplierId }).save();
      const ps = await check.default({
        supplierId,
        retailerId,
        userId: uuid.v4(),
        couponCode: 'TEST5',
        purchaser: {
          orderedAt: null,
        },
      });
      expect(sortBy(ps)).to.deep.equal(
        sortBy(promotionsInclCoupons.map(check.formatPromotion))
      );
    });

    it('finds coupon code if coupon code (no filters) sent', async () => {
      const otherSupplierId = uuid.v4();
      await promotionFactory({ hostId: otherSupplierId }).save();
      const ps = await check.default({
        supplierId,
        retailerId,
        userId: uuid.v4(),
        couponCode: couponWithNoFilter,
        purchaser: {
          orderedAt: null,
        },
      });
      expect(sortBy(ps)).to.deep.equal(
        sortBy(promotionsInclCouponsWithNoFilter.map(check.formatPromotion))
      );
    });

    it('does not send coupon code promotion if coupon code not present', async () => {
      const otherSupplierId = uuid.v4();
      await promotionFactory({ hostId: otherSupplierId }).save();
      const ps = await check.default({
        supplierId,
        retailerId,
        userId: uuid.v4(),
        couponCode: null,
        purchaser: {
          orderedAt: null,
        },
      });
      expect(sortBy(ps)).to.deep.equal(
        sortBy(promotions.map(check.formatPromotion))
      );
    });

    it('ignores promotions that have not started yet', async () => {
      const otherSupplierId = uuid.v4();
      await promotionFactory({
        hostId: otherSupplierId,
        startAt: new Date(Date.now() + 1000).toISOString(),
      }).save();

      const ps = await check.findPromotions({
        retailerId,
        supplierId,
        profile: {},
        userId: '',
      });
      expect(sortBy(ps)).to.deep.equal(promotions);
    });

    it('ignores promotions that have ended', async () => {
      const otherSupplierId = uuid.v4();
      await promotionFactory({
        hostId: otherSupplierId,
        finishAt: new Date(Date.now() - 1000).toISOString(),
      }).save();

      const ps = await check.findPromotions({
        retailerId,
        supplierId,
        profile: {},
        userId: '',
      });
      expect(sortBy(ps)).to.deep.equal(promotions);
    });
  });

  describe('evaluateConditions', () => {
    const f = check.evaluateConditions;
    const cart = {
      one: 1,
      four: 4,
      aOne: { a: 1 },
      list: [1],
      objList: [{ a: 1 }, { a: 4 }, { b: 3 }],
      empty: [],
      null: null,
    };

    describe('simple combinators', () => {
      const conditions: [Combinator, any, any, any, boolean][] = [
        ['and', true, true, null, true],
        ['and', false, true, null, false],
        ['or', true, true, null, true],
        ['or', false, true, null, true],
        ['equal', 'one', 2, null, false],
        ['equal', 'one', 'foo', null, false],
        ['equal', 'one', 1, null, true],
        ['equal', 'aOne', 1, '_access.a', true],
        ['oneOf', 'one', [1, 2, 3], null, true],
        ['oneOf', 'one', [], null, false],
        ['oneOf', 'four', [1, 2, 3], null, false],
        ['oneOf', 'aOne', [1, 2, 3], '_access.a', true],
        ['any', 'empty', [], null, false],
        ['any', 'list', [], null, false],
        ['any', 'empty', [1], null, false],
        ['any', 'list', [1], null, true],
        ['any', 'one', [1], null, true],
        ['any', 'list', 1, null, true],
        ['any', 'objList', [1, 3], '_access.a', true],
        ['any', 'objList', [3], '_access.a', false],
      ];

      conditions.forEach(
        ([combinator, leftNode, rightNode, modifier, expected], i) => {
          it(`${i}: ${leftNode} ${combinator} ${rightNode} === ${expected}`, () => {
            assert(
              _.isEqual(
                f(cart, { combinator, leftNode, rightNode, modifier }),
                expected
              )
            );
          });
        }
      );
    });

    describe('nested combinators', () => {
      it('recursively evaluates', () => {
        const qualifier: check.Conditions = {
          combinator: 'and',
          leftNode: { combinator: 'equal', leftNode: 'one', rightNode: 1 },
          rightNode: {
            combinator: 'any',
            leftNode: 'objList',
            rightNode: [2, 3],
            negate: true,
            modifier: '_access.c',
          },
        };

        assert(f(cart, qualifier));
      });
    });
  });

  describe('isPromotionApplicable', () => {
    const f = check.isPromotionApplicable;
    const supplierId = uuid.v4();

    it('is not applicable if promotion has no conditions', () => {
      const promotion = promotionFactory({
        hostId: supplierId,
        conditions: null,
      });

      assert(!f({ coupon: 'aaaa111', a: 1 }, promotion));
    });

    it('is applicable if a coupon promotion has no conditions', () => {
      const promotion = promotionFactory({
        hostId: supplierId,
        conditions: null,
        coupon: 'TEST',
      });

      assert(f({ coupon: 'aaaa111', a: 1 }, promotion));
    });
  });

  describe('findApplicablePromotionEffects', () => {
    const supplierId = uuid.v4();
    const f = check.findApplicablePromotionEffects;

    const applicable = promotionFactory({
      hostId: supplierId,
      conditions: {
        combinator: 'equal',
        leftNode: 'a',
        rightNode: 1,
      },
      effects: [check.effects.setCartDiscountAbsolute(5)],
    });

    const applicableNumberCheckCondition = promotionFactory({
      hostId: supplierId,
      conditions: {
        combinator: 'equal',
        leftNode: 'total',
        rightNode: '10.000',
      },
      effects: [check.effects.setCartDiscountAbsolute(5)],
    });

    const notApplicable = promotionFactory({
      hostId: supplierId,
      conditions: {
        combinator: 'equal',
        leftNode: 'b',
        rightNode: 1,
      },
      effects: [check.effects.setCartDiscountAbsolute(10)],
    });

    const cart = {
      a: 1,
      b: 2,
      coupon: 'aaaa111',
      total: '10',
    };

    it('only includes applicable effects', () => {
      expect(sortBy(f(cart, [applicable, notApplicable]))).to.deep.equal(
        sortBy([check.formatPromotion(applicable)])
      );
    });

    it('includes number parsed checking', () => {
      expect(sortBy(f(cart, [applicableNumberCheckCondition]))).to.deep.equal(
        sortBy([check.formatPromotion(applicableNumberCheckCondition)])
      );
    });
  });

  describe('Pre existing conditions', () => {
    let supplierId: string;
    let retailerId: string;
    let sandbox: sinon.SinonSandbox;

    beforeEach(() => {
      supplierId = uuid.v4();
      retailerId = uuid.v4();
      sandbox = sinon.createSandbox();
    });

    afterEach(() => {
      sandbox.restore();
    });

    const testSuites = [
      {
        title: 'should apply applicable promotion if orderedAt is null',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.firstOrder',
          rightNode: 'true',
        },
        cartData: {
          purchaser: { orderedAt: null },
        },
      },
      {
        title: 'should not apply applicable promotion if orderedAt is date',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.firstOrder',
          rightNode: 'true',
        },
        cartData: {
          purchaser: { orderedAt: new Date(Date.now()) },
        },
        negativeTestCase: true,
      },
      {
        title: 'should apply applicable promotion if orderedAt is date',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.firstOrder',
          rightNode: 'false',
        },
        cartData: {
          purchaser: { orderedAt: new Date(Date.now()) },
        },
      },
      {
        title: 'should not apply applicable promotion if orderedAt is null',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.firstOrder',
          rightNode: 'false',
        },
        cartData: {
          purchaser: { orderedAt: null },
        },
        negativeTestCase: true,
      },
      {
        title: 'should apply applicable promotion if cartTotal is gte to 1000',
        leftNode: {
          combinator: 'gte',
          leftNode: 'total',
          rightNode: '1000',
        },
        cartData: {
          total: '1001',
        },
      },
      {
        title:
          'should not apply applicable promotion if cartTotal is not gte to 999',
        leftNode: {
          combinator: 'gte',
          leftNode: 'total',
          rightNode: '1000',
        },
        cartData: {
          total: '999',
        },
        negativeTestCase: true,
      },
      {
        title: 'should apply applicable promotion if cartTotal is lte to 1000',
        leftNode: {
          combinator: 'lte',
          leftNode: 'total',
          rightNode: '1000',
        },
        cartData: {
          total: '999',
        },
      },
      {
        title:
          'should not apply applicable promotion if cartTotal is not lte to 1000',
        leftNode: {
          combinator: 'lte',
          leftNode: 'total',
          rightNode: '1000',
        },
        cartData: {
          total: '10001',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if cartTotal is equal to 1000',
        leftNode: {
          combinator: 'equal',
          leftNode: 'total',
          rightNode: '1000',
        },
        cartData: {
          total: '1000',
        },
      },
      {
        title:
          'should not apply applicable promotion if cartTotal is not equal to 1000',
        leftNode: {
          combinator: 'equal',
          leftNode: 'total',
          rightNode: '1000',
        },
        cartData: {
          total: '10001',
        },
        negativeTestCase: true,
      },
      {
        title: 'should apply applicable promotion for hasOrdered is true',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.hasOrdered',
          rightNode: 'true',
        },
        cartData: {
          purchaser: {
            orderedAt: new Date(Date.now()),
          },
        },
      },
      {
        title: 'should not apply applicable promotion if hasOrdered is true',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.hasOrdered',
          rightNode: 'true',
        },
        cartData: {
          purchaser: {
            orderedAt: null,
          },
        },
        negativeTestCase: true,
      },
      {
        title: 'should apply applicable promotion if hasOrdered is false',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.hasOrdered',
          rightNode: 'false',
        },
        cartData: {
          purchaser: {
            orderedAt: null,
          },
        },
      },
      {
        title: 'should not apply applicable promotion if hasOrdered is false',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.hasOrdered',
          rightNode: 'false',
        },
        cartData: {
          purchaser: {
            orderedAt: new Date(Date.now()),
          },
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion for paymentType equals to card',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.defaultPaymentMethodType',
          rightNode: 'card',
        },
        cartData: {
          purchaser: {
            defaultPaymentMethodType: 'card',
          },
        },
      },
      {
        title:
          'should not apply applicable promotion if paymentType is not equal to card',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.defaultPaymentMethodType',
          rightNode: 'card',
        },
        cartData: {
          purchaser: {
            defaultPaymentMethodType: 'notcard',
          },
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if paymentType is equals to direct',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.defaultPaymentMethodType',
          rightNode: 'direct',
        },
        cartData: {
          purchaser: {
            defaultPaymentMethodType: 'direct',
          },
        },
      },
      {
        title:
          'should not apply applicable promotion if paymentType is not equal to direct',
        leftNode: {
          combinator: 'equal',
          leftNode: 'purchaser',
          modifier: '_access.defaultPaymentMethodType',
          rightNode: 'direct',
        },
        cartData: {
          purchaser: {
            defaultPaymentMethodType: 'notdirect',
          },
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if cartSubtotal is gte to 1000',
        leftNode: {
          combinator: 'gte',
          leftNode: 'totalCost',
          rightNode: '1000',
        },
        cartData: {
          totalCost: '1001',
        },
      },
      {
        title:
          'should not apply applicable promotion if cartSubtotal is not gte to 1000',
        leftNode: {
          combinator: 'gte',
          leftNode: 'totalCost',
          rightNode: '1000',
        },
        cartData: {
          totalCost: '999',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if cartSubtotal is lte to 1000',
        leftNode: {
          combinator: 'lte',
          leftNode: 'totalCost',
          rightNode: '1000',
        },
        cartData: {
          totalCost: '999',
        },
      },
      {
        title:
          'should not apply applicable promotion if cartSubtotal is not lte to 1000',
        leftNode: {
          combinator: 'lte',
          leftNode: 'totalCost',
          rightNode: '1000',
        },
        cartData: {
          totalCost: '1001',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply promotion applicable if cartSubtotal is equal to 1000',
        leftNode: {
          combinator: 'equal',
          leftNode: 'totalCost',
          rightNode: '1000',
        },
        cartData: {
          totalCost: '1000',
        },
      },
      {
        title:
          'should not apply applicable promotion if cartSubtotal is not equal to 1000',
        leftNode: {
          combinator: 'equal',
          leftNode: 'totalCost',
          rightNode: '1000',
        },
        cartData: {
          totalCost: '10001',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should applyapplicable promotion if freight total is gte to 1000',
        leftNode: {
          combinator: 'gte',
          leftNode: 'totalFreight',
          rightNode: '1000',
        },
        cartData: {
          totalFreight: '10001',
        },
      },
      {
        title:
          'should not apply applicable promotion if freight is not gte to 1000',
        leftNode: {
          combinator: 'gte',
          leftNode: 'totalFreight',
          rightNode: '1000',
        },
        cartData: {
          totalFreight: '999',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if freight total is lte to 1000',
        leftNode: {
          combinator: 'lte',
          leftNode: 'totalFreight',
          rightNode: '1000',
        },
        cartData: {
          totalFreight: '999',
        },
      },
      {
        title:
          'should not apply applicable promotion if freight total is not lte to 1000',
        leftNode: {
          combinator: 'lte',
          leftNode: 'totalFreight',
          rightNode: '1000',
        },
        cartData: {
          totalFreight: '9999',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if freight total is equal to 1000',
        leftNode: {
          combinator: 'equal',
          leftNode: 'totalFreight',
          rightNode: '1000',
        },
        cartData: {
          totalFreight: '1000',
        },
      },
      {
        title:
          'should not apply applicable promotion if freight total is not equal to 1000',
        leftNode: {
          combinator: 'equal',
          leftNode: 'totalFreight',
          rightNode: '1000',
        },
        cartData: {
          totalFreight: '10001',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if supplier category is equal to Coffee',
        leftNode: {
          modifier: '_access.dataAttributes.primaryCategory',
          combinator: 'equal',
          leftNode: 'supplier',
          rightNode: 'Coffee',
        },
        cartData: {
          supplier: {
            dataAttributes: {
              primaryCategory: 'Coffee',
            },
          },
        },
      },
      {
        title:
          'should not apply applicable promotion if supplier category is not equal to Coffee',
        leftNode: {
          modifier: '_access.dataAttributes.primaryCategory',
          combinator: 'equal',
          leftNode: 'supplier',
          rightNode: 'Coffee',
        },
        cartData: {
          supplier: {
            dataAttributes: {
              primaryCategory: 'Bread',
            },
          },
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if firstOrderInCategory is equal to true',
        leftNode: {
          combinator: 'equal',
          leftNode: 'firstOrderInCategory',
          rightNode: 'true',
        },
        cartData: {
          firstOrderInCategory: 'true',
        },
      },
      {
        title:
          'should not apply applicable promotion if firstOrderInCategory is equal to true',
        leftNode: {
          combinator: 'equal',
          leftNode: 'firstOrderInCategory',
          rightNode: 'true',
        },
        cartData: {
          firstOrderInCategory: 'false',
        },
        negativeTestCase: true,
      },
      {
        title:
          'should apply applicable promotion if firstOrderInCategory is equal to false',
        leftNode: {
          combinator: 'equal',
          leftNode: 'firstOrderInCategory',
          rightNode: 'false',
        },
        cartData: {
          firstOrderInCategory: 'false',
        },
      },
      {
        title:
          'should not apply applicable promotion if firstOrderInCategory is equal to false',
        leftNode: {
          combinator: 'equal',
          leftNode: 'firstOrderInCategory',
          rightNode: 'false',
        },
        cartData: {
          firstOrderInCategory: 'true',
        },
        negativeTestCase: true,
      },
    ];

    for (const {
      title,
      cartData,
      leftNode,
      negativeTestCase = false,
    } of testSuites) {
      // eslint-disable-next-line no-loop-func
      it(title, async () => {
        const conditions = {
          combinator: 'and',
          leftNode,
          rightNode: {
            combinator: 'equal',
            leftNode: 'supplierId',
            rightNode: supplierId,
          },
        };
        const applicable = await promotionFactory({
          hostId: supplierId,
          conditions,
          effects: [check.effects.setCartDiscountAbsolute(5)],
        }).save();

        const data = {
          supplierId,
          retailerId,
          userId: '',
          ...cartData,
          purchaser: {
            orderedAt: null,
            ...cartData?.purchaser,
          },
        };

        const actualResult = await check.default(data);
        const [promotionData] = actualResult;

        // assertion for negative test cases.
        if (negativeTestCase) {
          expect(actualResult.length).to.be.eq(0);
          expect(actualResult).to.be.deep.eq([]);
          expect(promotionData).to.be.undefined;
        } else {
          // assertion for positive test cases.
          // if greather than 0 there is a applicable promotion.
          expect(actualResult.length).to.be.gt(0);
          expect(promotionData.name).to.be.equal(applicable.name);
          expect(promotionData.hostId).to.be.equal(applicable.hostId);
          expect(promotionData.effects).to.deep.equal(applicable.effects);
          expect(applicable.conditions).to.deep.equal(conditions);
        }
      });
    }

    describe('promotions set by chargeable', () => {
      let applicable;
      let baseCartData;

      beforeEach(async () => {
        sandbox.stub(cache, 'get').resolves({ cohort: 'alexDeagzTestCohort' });
        applicable = await promotionFactory({
          hostId: supplierId,
          conditions: {
            leftNode: {
              leftNode: {
                leftNode: 'orderCount',
                rightNode: 12,
                combinator: 'lte',
              },
              rightNode: {
                leftNode: 'profile',
                modifier: '_access.cohort',
                rightNode: ['alexDeagzTestCohort'],
                combinator: 'any',
              },
              combinator: 'and',
            },
            rightNode: {
              leftNode: {
                leftNode: {
                  leftNode: 'purchaser',
                  modifier: '_access.createdAt',
                  rightNode: '2021-11-01 01:00:00.000000+00',
                  combinator: 'gte',
                },
                rightNode: {
                  leftNode: 'purchaser',
                  modifier: '_access.createdAt',
                  rightNode: '2021-11-30 13:00:00.000000+00',
                  combinator: 'lte',
                },
                combinator: 'and',
              },
              rightNode: {
                tag: 'chargeable',
                leftNode: 'purchaser',
                modifier: '_access.properties.chargeable',
                rightNode: true,
                combinator: 'equal',
              },
              combinator: 'and',
            },
            combinator: 'and',
          },
          effects: [check.effects.setCartDiscountAbsolute(5)],
        }).save();
        baseCartData = {
          supplierId,
          retailerId,
          userId: uuid.v4(),
          orderCount: 1,
          profile: {
            cohort: 'alexDeagzTestCohort',
          },
          purchaser: {
            createdAt: new Date('2021-11-05').toISOString(),
            defaultPaymentMethodType: 'direct',
          },
        };
      });
      it('should apply promotion applicable for chargeable', async () => {
        const cartData = {
          ...baseCartData,
          purchaser: {
            ...baseCartData.purchaser,
            properties: { chargeable: true },
          },
        };

        const actualResult = await check.default(cartData);

        expect(actualResult.length).to.deep.equal(1);
        expect(actualResult[0].name).to.deep.equal(applicable.name);
      });
      it('should not apply promotion applicable for not chargeable', async () => {
        const cartData = {
          ...baseCartData,
          purchaser: {
            ...baseCartData.purchaser,
            properties: { chargeable: false },
          },
        };

        const actualResult = await check.default(cartData);

        expect(actualResult.length).to.deep.equal(0);
      });
      it('should not apply promotion applicable for undefined chargeable', async () => {
        const cartData = {
          ...baseCartData,
          purchaser: {
            ...baseCartData.purchaser,
            properties: {},
          },
        };

        const actualResult = await check.default(cartData);

        expect(actualResult.length).to.deep.equal(0);
      });
      it('should not apply promotion applicable for null chargeable', async () => {
        const cartData = {
          ...baseCartData,
          purchaser: {
            ...baseCartData.purchaser,
            properties: { chargeable: null },
          },
        };

        const actualResult = await check.default(cartData);

        expect(actualResult.length).to.deep.equal(0);
      });
    });

    it('should apply promotion applicable for lastOrderedSince is equal to 3', async () => {
      const applicable = await promotionFactory({
        hostId: supplierId,
        conditions: {
          combinator: 'and',
          leftNode: {
            combinator: 'equal',
            leftNode: 'purchaser',
            modifier: '_access.lastOrderedSince',
            rightNode: '3',
          },
          rightNode: {
            combinator: 'equal',
            leftNode: 'supplierId',
            rightNode: supplierId,
          },
        },
        effects: [check.effects.setCartDiscountAbsolute(5)],
      }).save();

      const cartData = {
        supplierId,
        retailerId,
        userId: '',
        purchaser: {
          orderedAt: moment().subtract(3, 'days').toDate(),
        },
      };

      const actualResult = await check.default(cartData);

      expect(actualResult.length).to.deep.equal(1);
      expect(actualResult[0].name).to.deep.equal(applicable.name);
    });

    it('should apply promotion applicable for group1 customer property is equal to A', async () => {
      const applicable = await promotionFactory({
        hostId: supplierId,
        conditions: {
          combinator: 'and',
          leftNode: {
            combinator: 'equal',
            leftNode: 'purchaser',
            modifier: '_access.properties.group1',
            rightNode: 'A',
          },
          rightNode: {
            combinator: 'equal',
            leftNode: 'supplierId',
            rightNode: supplierId,
          },
        },
        effects: [check.effects.setCartDiscountAbsolute(5)],
      }).save();

      const cartData = {
        supplierId,
        retailerId,
        userId: '',
        purchaser: {
          properties: {
            group1: 'A',
          },
        },
      };

      const actualResult = await check.default(cartData);

      expect(actualResult.length).to.deep.equal(1);
      expect(actualResult[0].name).to.deep.equal(applicable.name);
    });
  });

  describe('GE Integration', () => {
    const f = check.default;

    it('Basic #1', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();

      const cart = {
        one: 1,
        objList: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        retailerId,
        coupon: 'abcd_111',
        purchaser: {
          orderedAt: null,
        },
        userId: '',
      };

      const applicable = await promotionFactory({
        name: 'food and dairy co example',
        hostId: supplierId,
        sponsored: true,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            modifier: '_access.orderedAt',
            combinator: 'equal',
            leftNode: 'purchaser',
            rightNode: null,
          },
          rightNode: {
            combinator: 'equal',
            leftNode: 'supplierId',
            rightNode: supplierId,
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('#GE', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        coupon: 'abcd_111',
        isFirstOrder: true,
        orderCount: 3,
        retailerId,
        purchaser: {
          orderedAt: null,
        },
        userId: '',
      };

      const applicable = await promotionFactory({
        name: 'GE Example',
        hostId: supplierId,
        sponsored: true,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            leftNode: 'isFirstOrder',
            combinator: 'equal',
            rightNode: true,
          },
          rightNode: {
            combinator: 'any',
            leftNode: 'retailerId',
            rightNode: [retailerId],
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('#GE 2', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        coupon: 'abcd_111',
        isFirstOrder: false,
        orderCount: 3,
        retailerId,
        purchaser: {
          orderedAt: null,
        },
        userId: '',
      };

      const applicable = await promotionFactory({
        name: 'GE Example #2',
        hostId: supplierId,
        sponsored: true,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            leftNode: 'orderCount',
            combinator: 'equal',
            rightNode: 3,
          },
          rightNode: {
            combinator: 'any',
            leftNode: 'retailerId',
            rightNode: [retailerId],
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('GE #3', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();
      const purchaserId = uuid.v4();

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        coupon: 'abcd_111',
        isFirstOrder: false,
        orderCount: 3,
        retailerId,
        purchaser: {
          id: purchaserId,
          orderedAt: null,
        },
        userId: '',
      };

      const applicable = await promotionFactory({
        name: 'GE Example #3',
        hostId: supplierId,
        sponsored: true,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            modifier: '_access.id',
            leftNode: 'purchaser',
            combinator: 'any',
            rightNode: [purchaserId],
          },
          rightNode: {
            combinator: 'any',
            leftNode: 'supplierId',
            rightNode: [supplierId],
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('GE #4 - Using Profile', async () => {
      const supplierId = uuid.v4();
      const purchaserId = uuid.v4();
      const retailerId = uuid.v4();
      const userId = uuid.v4();
      const cohort = '123';
      const profile = {
        cohort,
      };

      await cache.set(`profile:${userId}`, profile);

      const cart = {
        retailerId,
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        coupon: 'abcd_111',
        isFirstOrder: false,
        userId,
        orderCount: 3,
        purchaser: {
          id: purchaserId,
          orderedAt: null,
        },
      };

      const applicable = await promotionFactory({
        name: 'GE Example #3',
        hostId: supplierId,
        sponsored: true,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            modifier: '_access.id',
            leftNode: 'purchaser',
            combinator: 'any',
            rightNode: [purchaserId],
          },
          rightNode: {
            combinator: 'any',
            leftNode: 'profile',
            modifier: '_access.cohort',
            rightNode: [cohort],
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('GE #4 - Never expires', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();
      const purchaserId = uuid.v4();
      const userId = uuid.v4();
      const cohort = '123';
      const profile = {
        cohort,
      };

      await cache.set(`profile:${userId}`, profile);

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        retailerId,
        supplierId,
        coupon: 'abcd_111',
        isFirstOrder: false,
        userId,
        orderCount: 2,
        purchaser: {
          id: purchaserId,
          orderedAt: null,
        },
      };

      const applicable = await promotionFactory({
        name: 'GE Example #3',
        hostId: supplierId,
        sponsored: true,
        finishAt: null,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            leftNode: 'orderCount',
            combinator: 'lte',
            rightNode: '2',
          },
          rightNode: {
            combinator: 'any',
            leftNode: 'profile',
            modifier: '_access.cohort',
            rightNode: [cohort],
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
      await applicable.update({
        finishAt: moment().subtract(1, 'days').toISOString(),
      });
    });

    it('GE #4 - Only GE', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();
      const purchaserId = uuid.v4();
      const userId = uuid.v4();
      const cohort = '123';
      const profile = {
        cohort,
      };

      await cache.set(`profile:${userId}`, profile);

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        retailerId,
        coupon: 'abcd_111',
        isFirstOrder: false,
        userId,
        orderCount: 2,
        purchaser: {
          id: purchaserId,
          properties: { growthEngine: true },
          orderedAt: null,
        },
      };

      const applicable = await promotionFactory({
        name: 'GE Example #MAX',
        hostId: supplierId,
        sponsored: true,
        finishAt: null,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            leftNode: {
              leftNode: 'orderCount',
              combinator: 'lte',
              rightNode: '2',
            },
            rightNode: {
              combinator: 'any',
              leftNode: 'profile',
              modifier: '_access.cohort',
              rightNode: [cohort],
            },
            combinator: 'and',
          },
          rightNode: {
            combinator: 'equal',
            leftNode: 'purchaser',
            modifier: '_access.properties.growthEngine',
            rightNode: true,
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('GE #10 - Product Test', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();
      const purchaserId = uuid.v4();

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        retailerId,
        userId: uuid.v4(),
        coupon: 'abcd_111',
        isFirstOrder: false,
        orderCount: 15,
        purchaser: {
          id: purchaserId,
          properties: { growthEngine: true },
          orderedAt: null,
        },
      };

      const applicable = await promotionFactory({
        name: 'GE Example MEZ',
        hostId: supplierId,
        sponsored: true,
        finishAt: null,
        effects: [
          [
            {
              type: 'priceDiscount',
              value: { '22a6ae14-272f-4e31-91f2-f6989d0db1bc': '9' },
              target: 'cart',
            },
          ],
        ],
        conditions: {
          leftNode: {
            tag: 'purchaser',
            leftNode: 'purchaser',
            modifier: '_access.id',
            rightNode: [purchaserId],
            combinator: 'any',
          },
          rightNode: {
            leftNode: 'supplierId',
            rightNode: supplierId,
            combinator: 'equal',
          },
          combinator: 'and',
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('GE #10 - User Targeting', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();
      const purchaserId = uuid.v4();
      const userId = uuid.v4();
      const profile = {
        email: '<EMAIL>',
      };

      await cache.set(`profile:${userId}`, profile);

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        retailerId,
        coupon: 'abcd_111',
        isFirstOrder: false,
        userId,
        orderCount: 15,
        purchaser: {
          id: purchaserId,
          properties: { growthEngine: true },
          orderedAt: null,
        },
      };

      const applicable = await promotionFactory({
        name: 'GE Example #10',
        hostId: supplierId,
        sponsored: true,
        finishAt: null,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          leftNode: {
            leftNode: 'orderCount',
            combinator: 'lte',
            rightNode: '20',
          },
          rightNode: {
            combinator: 'equal',
            leftNode: 'profile',
            modifier: '_access.email',
            rightNode: '<EMAIL>',
          },
          combinator: 'and',
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    it('GE #4 - Marketplace and GE', async () => {
      const supplierId = uuid.v4();
      const retailerId = uuid.v4();
      const purchaserId = uuid.v4();
      const userId = uuid.v4();
      const cohort = '123';
      const profile = {
        cohort,
      };

      await cache.set(`profile:${userId}`, profile);

      const cart = {
        one: 1,
        lineItems: [{ a: 1 }, { a: 2 }, { b: 3 }],
        supplierId,
        retailerId,
        coupon: 'abcd_111',
        isFirstOrder: false,
        userId,
        orderCount: 2,
        purchaser: {
          id: purchaserId,
          properties: { growthEngine: true },
          orderedAt: null,
        },
      };

      const applicable = await promotionFactory({
        name: 'GE Example #MAX',
        hostId: supplierId,
        sponsored: true,
        finishAt: null,
        effects: [check.effects.setCartDiscountAbsolute(50)],
        conditions: {
          combinator: 'and',
          leftNode: {
            leftNode: {
              leftNode: 'orderCount',
              combinator: 'lte',
              rightNode: '2',
            },
            rightNode: {
              combinator: 'any',
              leftNode: 'profile',
              modifier: '_access.cohort',
              rightNode: [cohort],
            },
            combinator: 'and',
          },
          rightNode: {
            combinator: 'or',
            leftNode: {
              combinator: 'equal',
              leftNode: 'purchaser',
              modifier: '_access.properties.marketplacePurchaser',
              rightNode: true,
            },
            rightNode: {
              combinator: 'equal',
              leftNode: 'purchaser',
              modifier: '_access.properties.growthEngine',
              rightNode: true,
            },
          },
        },
      }).save();

      const expected = [check.formatPromotion(applicable)];

      expect(sortBy(await f(cart))).to.deep.equal(sortBy(expected));
    });

    describe('Promotions purchaser filter integration tests', () => {
      let promotion;
      let supplierId;
      let cart;

      beforeEach(async () => {
        supplierId = uuid.v4();
        cart = {
          one: 1,
          objList: [{ a: 1 }, { a: 2 }, { b: 3 }],
          supplierId,
          purchaser: {
            orderedAt: null,
          },
          userId: '',
        };
      });

      it('Promotion should be applicable to only purchasers in list', async () => {
        const purchaserId = uuid.v4();
        const purchaserId2 = uuid.v4();
        const purchaserId3 = uuid.v4();
        const notIncludedPurchaserId = uuid.v4();
        const includedPurchasers = [purchaserId, purchaserId2, purchaserId3];

        cart.purchaser = {
          ...cart.purchaser,
          id: purchaserId,
        };

        promotion = await promotionFactory({
          hostId: supplierId,
          conditions: {
            combinator: 'and',
            leftNode: {
              combinator: 'oneOf',
              leftNode: 'purchaser',
              modifier: '_access.id',
              rightNode: includedPurchasers,
            },
            rightNode: {
              combinator: 'equal',
              leftNode: 'supplierId',
              rightNode: supplierId,
            },
          },
          effects: [check.effects.setCartDiscountAbsolute(25)],
        }).save();

        const formattedPromotion = [check.formatPromotion(promotion)];

        const actual = await f(cart);
        cart.purchaser.id = purchaserId2;
        const actual2 = await f(cart);
        cart.purchaser.id = purchaserId3;
        const actual3 = await f(cart);
        cart.purchaser.id = notIncludedPurchaserId;
        const actual4 = await f(cart);

        expect(sortBy(actual)).to.deep.equal(sortBy(formattedPromotion));
        expect(sortBy(actual2)).to.deep.equal(sortBy(formattedPromotion));
        expect(sortBy(actual3)).to.deep.equal(sortBy(formattedPromotion));
        expect(sortBy(actual4)).to.deep.equal(sortBy([]));
      });
    });
  });

  describe('applyCombinator', () => {
    const todayDate = moment().toDate();
    const yesterdayDate = moment().subtract(1, 'days').toDate();
    const testSuites = {
      and: [
        {
          left: true,
          right: true,
          result: true,
        },
        {
          left: false,
          right: true,
          result: false,
        },
      ],
      or: [
        {
          left: false,
          right: true,
          result: true,
        },
        {
          left: true,
          right: false,
          result: true,
        },
        {
          left: false,
          right: false,
          result: false,
        },
      ],
      equal: [
        {
          right: 5,
          mleft: 5,
          result: true,
        },
        {
          right: 5,
          mleft: 4,
          result: false,
        },
        {
          right: true,
          mleft: true,
          result: true,
        },
        {
          right: false,
          mleft: true,
          result: false,
        },
        {
          right: false,
          mleft: false,
          result: true,
        },
        {
          right: todayDate,
          mleft: todayDate,
          result: true,
        },
        {
          right: todayDate,
          mleft: yesterdayDate,
          result: false,
        },
      ],
      gt: [
        {
          right: '0',
          mleft: 0,
          result: false,
        },
        {
          right: '0',
          mleft: 6,
          result: true,
        },
        {
          right: '6',
          mleft: 2,
          result: false,
        },
        {
          right: yesterdayDate,
          mleft: todayDate,
          result: true,
        },
        {
          right: todayDate,
          mleft: yesterdayDate,
          result: false,
        },
      ],
      gte: [
        {
          right: '0',
          mleft: 0,
          result: true,
        },
        {
          right: '0',
          mleft: 6,
          result: true,
        },
        {
          right: '6',
          mleft: 2,
          result: false,
        },
        {
          right: todayDate,
          mleft: todayDate,
          result: true,
        },
        {
          right: yesterdayDate,
          mleft: todayDate,
          result: true,
        },
        {
          right: todayDate,
          mleft: yesterdayDate,
          result: false,
        },
      ],
      lt: [
        {
          right: '0',
          mleft: 0,
          result: false,
        },
        {
          right: '0',
          mleft: 6,
          result: false,
        },
        {
          right: '6',
          mleft: 0,
          result: true,
        },
        {
          right: todayDate,
          mleft: yesterdayDate,
          result: true,
        },
        {
          right: yesterdayDate,
          mleft: todayDate,
          result: false,
        },
      ],
      lte: [
        {
          right: '0',
          mleft: 0,
          result: true,
        },
        {
          right: '0',
          mleft: 6,
          result: false,
        },
        {
          right: '6',
          mleft: 0,
          result: true,
        },
        {
          right: todayDate,
          mleft: todayDate,
          result: true,
        },
        {
          right: yesterdayDate,
          mleft: todayDate,
          result: false,
        },
        {
          right: todayDate,
          mleft: yesterdayDate,
          result: true,
        },
      ],
      any: [
        {
          right: ['1bd1cca8-2035-4cb8-b882-7c16fbd75488'],
          mleft: '1bd1cca8-2035-4cb8-b882-7c16fbd75488',
          result: true,
        },
        {
          right: ['1bd1cca8-2035-4cb8-b882-7c16fbd75482'],
          mleft: '1bd1cca8-2035-4cb8-b882-7c16fbd75488',
          result: false,
        },
      ],
    };

    for (const suite of Object.keys(testSuites)) {
      context(suite, () => {
        for (const { left, mleft, right, result } of testSuites[suite]) {
          it(`should return ${result} if the condiitons of "${suite}" combinator are ${
            result ? '' : 'not '
          }valid`, () => {
            const valid = applyCombinator(
              suite as Combinator,
              left,
              mleft,
              right
            );

            expect(valid).to.be.equal(result);
          });
        }
      });
    }
  });

  describe('checkCouponUsage', () => {
    let hostId;
    let userId;
    let purchaserId;
    let retailerId;
    let payload;

    beforeEach(() => {
      hostId = uuid.v4();
      userId = uuid.v4();
      purchaserId = uuid.v4();
      retailerId = uuid.v4();
      payload = {
        name: Math.random().toString(),
        description: '',
        conditions: [],
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon: 'TEST',
        usage: 0,
      };
    });

    it('should return data if usage is null', async () => {
      await promotionFactory({ ...payload, usage: null }).save();

      const promotions = await checkCouponUsage(
        {
          supplierId: hostId,
          couponCode: payload.coupon,
          userId,
          purchaserId,
          retailerId,
        },
        ''
      );

      const properties = ['name', 'coupon', 'hostId'];

      for (const property of properties) {
        // @ts-ignore
        expect(promotions[property]).to.be.eq(payload[property]);
      }
    });

    it('should return data if usage is 0 (no usage data)', async () => {
      await promotionFactory({ ...payload, usage: 0 }).save();

      const promotions = await checkCouponUsage(
        {
          supplierId: hostId,
          couponCode: payload.coupon,
          userId,
          purchaserId,
          retailerId,
        },
        ''
      );

      const properties = ['name', 'usage', 'coupon', 'hostId'];

      for (const property of properties) {
        // @ts-ignore
        expect(promotions[property]).to.be.eq(payload[property]);
      }
    });

    it('should not return data if usage is 0 (has 1 usage data)', async () => {
      const promotion = await promotionFactory({ ...payload, usage: 0 }).save();

      await PromotionsUsage.create({
        supplierId: hostId,
        retailerId: uuid.v4(),
        userId,
        purchaserId,
        discounts: [
          {
            coupon: promotion.coupon,
            name: promotion.name,
            sponsored: promotion.sponsored,
            hostId: promotion.hostId,
          },
        ],
      });

      const promotions = await checkCouponUsage(
        {
          supplierId: hostId,
          couponCode: promotion.coupon,
          userId,
          purchaserId,
          retailerId,
        },
        ''
      );

      expect(promotions).to.be.deep.eq([]);
    });

    it('should return data if usage is 2 (no usage data)', async () => {
      const promotion = await promotionFactory({ ...payload, usage: 2 }).save();

      await PromotionsUsage.create({
        supplierId: hostId,
        retailerId: uuid.v4(),
        userId,
        purchaserId,
        discounts: [
          {
            coupon: promotion.coupon,
            name: promotion.name,
            hostId: promotion.hostId,
            sponsored: promotion.sponsored,
          },
        ],
      });

      const promotions = await checkCouponUsage(
        {
          supplierId: hostId,
          couponCode: payload.coupon,
          userId,
          purchaserId,
          retailerId,
        },
        ''
      );

      const properties = ['name', 'coupon', 'hostId'];

      for (const property of properties) {
        // @ts-ignore
        expect(promotions[property]).to.be.eq(payload[property]);
      }
    });

    it('should not return data if used count is 2 (has 2 usage data)', async () => {
      const promotion = await promotionFactory({ ...payload, usage: 2 }).save();
      await PromotionsUsage.create({
        supplierId: hostId,
        retailerId,
        userId,
        purchaserId,
        discounts: [
          {
            coupon: promotion.coupon,
            name: promotion.name,
            hostId: promotion.hostId,
            sponsored: promotion.sponsored,
          },
        ],
      });
      await PromotionsUsage.create({
        supplierId: hostId,
        retailerId,
        userId,
        purchaserId,
        discounts: [
          {
            coupon: promotion.coupon,
            name: promotion.name,
            hostId: promotion.hostId,
            sponsored: promotion.sponsored,
          },
        ],
      });

      const promotions = await checkCouponUsage(
        {
          supplierId: hostId,
          couponCode: payload.coupon,
          userId,
          purchaserId,
          retailerId,
        },
        ''
      );

      expect(promotions).to.be.deep.eq([]);
    });
  });
});
