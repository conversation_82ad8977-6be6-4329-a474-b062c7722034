import { Op } from 'sequelize';
import moment from 'moment';
import _ from 'lodash';
import { Promotion, PromotionsUsage } from '../../models';
import {
  PromotionAttributes,
  Combinator,
  PromotionInstance,
} from '../../models/promotion';

import cache from '../../config/cache';
import { logger } from '../../config';

type Cart = {
  supplierId: string;
  retailerId: string;
  userId: string;
  couponCode?: string;
  purchaser?: any;
  purchaserId?: string;
  supplier?: any;
  totalCost?: string;
  totalFreight?: string;
  firstOrderInCategory?: string;
  isFirstOrder?: boolean;
};

type Context = {
  profile: any;
} & Cart;

export const formatPromotion = (promotion: PromotionAttributes) =>
  _.pick(promotion, [
    'name',
    'sponsored',
    'hostId',
    'effects',
    'description',
    'coupon',
  ]);

export type Conditions = {
  combinator?: Combinator;
  leftNode: Conditions | unknown; // eslint-disable-line
  rightNode: Conditions | unknown; // eslint-disable-line
  modifier?: string;
  negate?: boolean;
};

export const effects = {
  setCartDiscountAbsolute(value: number) {
    return { target: 'cart', type: 'absolute', value };
  },
  setCartDiscountPercent(value: number) {
    return { target: 'cart', type: 'percent', value };
  },
};

/**
 * Return based on finishAt timestamp, so that ordermentum can sequence based on this priority
 *
 * Promo Overlaps
 *
 * I have one product (125g yoghurt) in two separate promotions for September.
 * Promo A has the yoghurt at a $0.20 discount starting 25/8 and expires 13/9.
 * Promo B has the yoghurt at a $0.25 discount and starts 7/9.
 *
 * The expectation is that the newest promo to activate, Promo B in this case, takes precedent over any existing promo and applies the $0.25 discount. Should Promo B expire before Promo A does, then OM would revert back to the $0.20 discount.
 */
export function findPromotions(cart: Context) {
  return Promotion.findAll({
    where: {
      [Op.or]: [{ hostId: cart.supplierId }, { hostId: { [Op.eq]: null } }],
      startAt: {
        [Op.lte]: Date.now(),
      },
      coupon: {
        [Op.eq]: null,
      },
      finishAt: {
        [Op.or]: [{ [Op.gte]: Date.now() }, { [Op.eq]: null }],
      },
    },
    order: [['finishAt', 'DESC']],
  });
}

export function findCouponCode(cart: Context) {
  return Promotion.findAll({
    where: {
      [Op.or]: [{ hostId: cart.supplierId }, { hostId: { [Op.eq]: null } }],
      startAt: {
        [Op.lte]: Date.now(),
      },
      coupon: cart.couponCode,
      finishAt: {
        [Op.or]: [{ [Op.gte]: Date.now() }, { [Op.eq]: null }],
      },
    },
  });
}

export function applyModifier(modifier: string | null, node: any) {
  if (modifier == null) return node;

  const fields = modifier.split('.');
  if (!Array.isArray(fields) || fields.length < 0) {
    return node;
  }

  if (Array.isArray(node)) {
    switch (fields[0]) {
      case '_access':
        return _.map(node, v => _.get(v, fields.slice(1)));
      default:
        return null;
    }
  }

  switch (fields[0]) {
    case '_access':
      return _.get(node, fields.slice(1));
    default:
      return null;
  }
}

export function xor(a: any, b: any) {
  if (a === b) return false;
  return true;
}

function getTypedValues(left: any, right: any): Number[] | Date[] {
  // Number.isNaN only returns true when passed Number.NaN
  // isNaN returns true whenever something isn't a valid Number
  // That's actually the test we want to be doing so ignore eslint here
  // eslint-disable-next-line no-restricted-globals
  if (!isNaN(left) && !isNaN(right)) {
    return [Number(left), Number(right)];
  }

  if (moment(left).isValid() && moment(right).isValid()) {
    return [moment(left).toDate(), moment(right).toDate()];
  }

  return [left, right];
}

/**
 * used by @applyCombinator method
 */
const checkNumberAndDateType = (
  leftValue: Number | Date,
  rightValue: Number | Date,
  method: string
) =>
  ((_.isNumber(leftValue) && _.isNumber(rightValue)) ||
    (_.isDate(leftValue) && _.isDate(rightValue))) &&
  _[method](leftValue, rightValue);

export function applyCombinator(
  combinator: Combinator,
  left: Conditions,
  mLeft: Conditions | any,
  right: Conditions | any
) {
  const [parsedLeft, parsedRight] = getTypedValues(mLeft, right);

  switch (combinator) {
    case 'and':
      return _.isBoolean(left) && _.isBoolean(right) && left && right;
    case 'or':
      return _.isBoolean(left) && _.isBoolean(right) && (left || right);
    case 'equal':
      return _.isEqual(parsedLeft, parsedRight);
    case 'gt':
      return checkNumberAndDateType(parsedLeft, parsedRight, combinator);
    case 'gte':
      return checkNumberAndDateType(parsedLeft, parsedRight, combinator);
    case 'lt':
      return checkNumberAndDateType(parsedLeft, parsedRight, combinator);
    case 'lte':
      return checkNumberAndDateType(parsedLeft, parsedRight, combinator);
    case 'oneOf':
      return _.includes(right, mLeft);
    case 'any':
      return _.intersection(_.flatten([mLeft]), _.flatten([right])).length > 0;
    default: {
      throw new Error(`unknown combinator ${combinator}`);
    }
  }
}

function isConditions(v: any): v is Conditions {
  if (v == null) return false;
  return v.combinator != null;
}

export function evaluateConditions(
  cart: {},
  conditions: Conditions,
  depth = 1
) {
  try {
    const {
      combinator,
      leftNode,
      rightNode,
      modifier,
      negate = false,
    } = conditions;

    const next = depth + 1;

    const left = isConditions(leftNode)
      ? evaluateConditions(cart, leftNode, next)
      : leftNode;
    const right = isConditions(rightNode)
      ? evaluateConditions(cart, rightNode, next)
      : rightNode;

    const mLeft = applyModifier(modifier, _.get(cart, left));
    return xor(negate, applyCombinator(combinator, left, mLeft, right));
  } catch (e) {
    return false;
  }
}

export function isPromotionApplicable(cart, promotion) {
  // Block sponsored promotions for purchasers with 'upfront' transaction preference
  if (
    promotion.sponsored &&
    cart.purchaser?.transactionPreference === 'upfront'
  ) {
    return false;
  }

  if (_.isEmpty(promotion.conditions) && promotion.coupon) {
    // No conditions only apply for coupon promotions
    return true;
  }
  return evaluateConditions(cart, promotion.conditions);
}

export function findApplicablePromotionEffects(cart, promotions) {
  const applicable = promotions.filter(p => isPromotionApplicable(cart, p));
  return applicable.map(formatPromotion);
}

export async function checkCouponUsage(
  cart: Cart,
  profile: any
): Promise<PromotionInstance | []> {
  const promotions = await findCouponCode({
    ...cart,
    profile,
    couponCode: cart.couponCode.toUpperCase(),
  });

  if (!promotions.length) {
    return [];
  }

  const [promotion] = promotions;
  const { usage, id } = promotion;
  const { userId, retailerId } = cart;

  // The rules for coupon code usage are as follows:
  // 1. If usage is null, the coupon code is unlimited.
  // 2. If usage is 0, the coupon code is single-use, ever, by anyone
  // 3. If usage is x, the usage is x times per retailer
  logger.info(
    { usage, retailerId, promotionId: id, userId },
    'Determining coupon usage'
  );

  if (usage === null) {
    return promotion;
  }

  if (usage === 0) {
    const totalUsageCount = await PromotionsUsage.count({
      where: {
        promotionId: id,
      },
    });
    // This is the case where the coupon code is single-use (usage: 0)
    // but it never been used (totalUsageCount: 0)
    if (totalUsageCount === 0) {
      return promotion;
    }
    return [];
  }

  // At this point, the usage is x, which is x usages per retailer
  const totalUsageCount = await PromotionsUsage.count({
    where: {
      promotionId: id,
      retailerId,
    },
  });

  // multiple usage
  if (totalUsageCount < usage) {
    return promotion;
  }

  return [];
}

export default async function check(cart: Cart): Promise<Array<any>> {
  let profile = {};
  const { userId, purchaser } = cart;
  if (userId) {
    profile = await cache.get(`profile:${userId}`, {});
  }

  const promotions = await Promise.all([
    findPromotions({ ...cart, profile }),
    cart?.couponCode ? checkCouponUsage(cart, profile) : [],
  ]);
  const combinedPurchaserData = {
    ...purchaser,
    firstOrder: cart.isFirstOrder ?? !purchaser.orderedAt ? 'true' : 'false',
    hasOrdered: purchaser.orderedAt ? 'true' : 'false',
    lastOrderedSince: purchaser.orderedAt
      ? moment().diff(purchaser.orderedAt, 'days').toString()
      : null,
  };

  const combinedCartData = {
    ...cart,
    firstOrderInCategory: cart?.firstOrderInCategory?.toString(),
    purchaser: combinedPurchaserData,
    profile,
  };

  const applicable = findApplicablePromotionEffects(
    combinedCartData,
    _.flatten(promotions)
  );

  return applicable;
}
